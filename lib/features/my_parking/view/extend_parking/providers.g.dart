// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$extendParkingNotifierHash() =>
    r'bb1fa9b2d18467028204b26a70f7036f7278eca7';

/// See also [ExtendParkingNotifier].
@ProviderFor(ExtendParkingNotifier)
final extendParkingNotifierProvider = AutoDisposeNotifierProvider<
    ExtendParkingNotifier, ExtendParkingState>.internal(
  ExtendParkingNotifier.new,
  name: r'extendParkingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$extendParkingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ExtendParkingNotifier = AutoDisposeNotifier<ExtendParkingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
