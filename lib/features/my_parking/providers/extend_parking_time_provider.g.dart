// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'extend_parking_time_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$extendParkingTimeNotifierHash() =>
    r'062c81bba874224828ed64bc09fdf5cc89b1f214';

/// See also [ExtendParkingTimeNotifier].
@ProviderFor(ExtendParkingTimeNotifier)
final extendParkingTimeNotifierProvider = AutoDisposeNotifierProvider<
    ExtendParkingTimeNotifier, ExtendParkingTimeState>.internal(
  ExtendParkingTimeNotifier.new,
  name: r'extendParkingTimeNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$extendParkingTimeNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ExtendParkingTimeNotifier
    = AutoDisposeNotifier<ExtendParkingTimeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
