// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settle_all_violation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$settleAllViolationNotifierHash() =>
    r'f1add0fc9c91d55e114419b4c8660ef7715c7790';

/// See also [SettleAllViolationNotifier].
@ProviderFor(SettleAllViolationNotifier)
final settleAllViolationNotifierProvider = AutoDisposeNotifierProvider<
    SettleAllViolationNotifier, SettleAllViolationState>.internal(
  SettleAllViolationNotifier.new,
  name: r'settleAllViolationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$settleAllViolationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SettleAllViolationNotifier
    = AutoDisposeNotifier<SettleAllViolationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
