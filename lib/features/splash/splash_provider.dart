import 'dart:convert';

import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/features/home/<USER>/home_provider.dart';
import 'package:albalad_parking_app/features/profile/model/profile_model.dart';
import 'package:albalad_parking_app/helper/secure_storage_helper.dart';
import 'package:albalad_parking_app/helper/shared_preference_helper.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:albalad_parking_app/utils/urls.dart';

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'splash_provider.g.dart';

@riverpod
class SplashNotifier extends _$SplashNotifier {
  @override
  Map<String, dynamic> build() {
    // loadAppData();
    loadWithEnvironment();
    return {};
  }

  loadAppData() async {
    Future.delayed(const Duration(seconds: 3), () async {
      Map<String, dynamic> newState = {};

      // Load tokens from secure storage
      await TokenService.loadTokens();

      final userJson = await SecureStorageHelper.instance.getData('user');
      if (userJson != null) {
        final json = jsonDecode(userJson);
        if (json['uid'] != null) {
          LoggedInUser.fromJson(json);
          final profileJson =
              SharedPreferenceHelper.instance.getData('profile');
          if (profileJson != null) {
            final proJson = jsonDecode(profileJson);
            if (proJson != null) {
              ProfileModel.fromJson(proJson);
            }
          }
          newState['isLogined'] = true;
        }
      } else {
        newState['isLogined'] = false;
      }

      try {
        var loc = await ref.read(fetchLocationProvider.future);

        ref.read(myLocationProvider.notifier).state = loc;
      } catch (e, stackTracec) {
        debugPrint(e.toString());
        debugPrint(stackTracec.toString());
      }
      state = newState;
    });
  }

  loadWithEnvironment() {
    ApiConstants.initializeEnvironment();
    Future.delayed(
      const Duration(seconds: 3),
      () async {
        Map<String, dynamic> newState = {};

        // Load tokens from secure storage
        await TokenService.loadTokens();

        // Environment is already initialized in main.dart
        final userJson = await SecureStorageHelper.instance.getData('user');
        if (userJson != null) {
          final json = jsonDecode(userJson);
          if (json['uid'] != null) {
            LoggedInUser.fromJson(json);
            final profileJson =
                SharedPreferenceHelper.instance.getData('profile');
            if (profileJson != null) {
              final proJson = jsonDecode(profileJson);
              if (proJson != null) {
                ProfileModel.fromJson(proJson);
              }
            }
            newState['isLogined'] = true;
          }
        } else {
          newState['isLogined'] = false;
        }
        try {
          var loc = await ref.read(fetchLocationProvider.future);

          ref.read(myLocationProvider.notifier).state = loc;
        } catch (e, stackTracec) {
          debugPrint(e.toString());
          debugPrint(stackTracec.toString());
        }
        state = newState;
      },
    );
  }
}
