// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_up_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$signUpNotifierHash() => r'39741cc39c950e11ab374a2ce9b959de6cbe1340';

/// See also [SignUpNotifier].
@ProviderFor(SignUpNotifier)
final signUpNotifierProvider =
    AutoDisposeNotifierProvider<SignUpNotifier, SignUpState>.internal(
  SignUpNotifier.new,
  name: r'signUpNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signUpNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignUpNotifier = AutoDisposeNotifier<SignUpState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
