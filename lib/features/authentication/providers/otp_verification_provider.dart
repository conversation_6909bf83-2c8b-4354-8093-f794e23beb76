// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:developer';

import 'package:albalad_parking_app/app/app.dart';
import 'package:albalad_parking_app/features/authentication/models/auth_success_model.dart';
import 'package:albalad_parking_app/features/authentication/providers/otp_verification_state.dart';
import 'package:albalad_parking_app/features/authentication/services/auth_services.dart';
import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/helper/dialog_helper.dart';
import 'package:albalad_parking_app/helper/secure_storage_helper.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:albalad_parking_app/utils/translation.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'otp_verification_provider.g.dart';

// A StateProvider to hold the current sign-in response model.
// It is initialized with null, indicating no sign-in response exists initially.
// This can be updated with a `SignInModel` instance upon successful sign-in
// to share the response data across the app where needed.
final authResponseProvider = StateProvider<AuthSuccessModel?>((ref) => null);

@riverpod
class OtpVerificationNotifier extends _$OtpVerificationNotifier {
  @override
  OtpVerificationState build() => OtpVerificationInitial();

  void clearOTP() async {
    state = OtpVerificationInitial();
  }

  Future<void> verifyOtp({
    required String verificationURL,
    required String otp,
    required ({
      String enterValid,
      String success,
      String failure,
      String error
    }) validations,
  }) async {
    try {
      state = OtpVerificationLoading();
      if (otp.length != 4) {
        state = OtpVerificationError(validations.enterValid);
        return;
      }

      final response = await AuthServices()
          .verifyOTP(verificationURL: verificationURL, otp: otp);

      if (response.statusCode == 200 && response.data['result'] == "success") {
        final json = response.data;
        final records = json['records'];
        LoggedInUser.fromJson(records);

        // Save user data to secure storage
        SecureStorageHelper.instance.saveData('user', jsonEncode(records));

        // Save tokens using TokenService if available
        if (records['token'] != null) {
          final tokenData = records['token'];
          await TokenService.saveTokens(
            accessToken: tokenData['access_token'] ?? '',
            refreshToken: tokenData['refresh_token'] ?? '',
            expiresIn: tokenData['expires_in']?.toString(),
          );
        }

        state = OtpVerificationSuccess(validations.success);
      } else if (response.statusCode == 400 &&
          response.data['result'] == "failure") {
        if (response.data["message"] != null) {
          String message = response.data["message"];
          state = OtpVerificationError(message);
        }
        if (response.data["errors"] != null) {
          Map<String, dynamic> errors = response.data['errors'];
          if (errors.isNotEmpty) {
            state = OtpVerificationError(errors.values.first);
          }
        }
      } else {
        state = OtpVerificationError(validations.failure);
      }
    } on DioException catch (e) {
      log(e.type.toString());
      state = OtpVerificationError(validations.error);
      if (e.type == DioExceptionType.connectionError) {
        BuildContext? context = navigatorKey.currentState?.context;
        DialogHelper.showErrorBottomSheet(
            context: context!, message: getNoInternetTranslation());
      }
    }
  }

  Future<bool> resendOTP({required String resendOtpURL}) async {
    try {
      final response =
          await AuthServices().resendOTP(resendOtpURL: resendOtpURL);
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = OtpVerificationResendCode(response.data['message']);
          return true;
        }
      } else {
        state = OtpVerificationError('Something went wrong!');
      }
      return false;
    } on DioException catch (e) {
      state = OtpVerificationError("An error occurred. Please try again.");
      if (e.type == DioExceptionType.connectionError) {
        BuildContext? context = navigatorKey.currentState?.context;
        DialogHelper.showErrorBottomSheet(
            context: context!, message: getNoInternetTranslation());
      }
      return false;
    }
  }
}
