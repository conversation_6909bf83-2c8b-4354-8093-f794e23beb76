import 'dart:convert';
import 'dart:developer';
import 'package:albalad_parking_app/helper/secure_storage_helper.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:albalad_parking_app/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class TokenService {
  static const String _tokenStorageKey = 'auth_tokens';

  /// Save tokens to secure storage
  static Future<bool> saveTokens({
    required String accessToken,
    required String refreshToken,
    String? expiresIn,
  }) async {
    try {
      // Update in-memory tokens
      Token.accessToken = accessToken;
      Token.refreshToken = refreshToken;
      Token.expiresIn = expiresIn;

      // Save to secure storage
      final tokenData = {
        'access_token': accessToken,
        'refresh_token': refreshToken,
        'expires_in': expiresIn,
      };

      await SecureStorageHelper.instance.saveData(
        _tokenStorageKey,
        jsonEncode(tokenData),
      );

      if (kDebugMode) {
        log('Tokens saved successfully', name: 'TokenService');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        log('Error saving tokens: $e', name: 'TokenService');
      }
      return false;
    }
  }

  /// Load tokens from secure storage
  static Future<bool> loadTokens() async {
    try {
      final tokenString =
          await SecureStorageHelper.instance.getData(_tokenStorageKey);

      if (tokenString != null && tokenString.isNotEmpty) {
        final tokenData = jsonDecode(tokenString) as Map<String, dynamic>;

        Token.accessToken = tokenData['access_token'];
        Token.refreshToken = tokenData['refresh_token'];
        Token.expiresIn = tokenData['expires_in'];

        if (kDebugMode) {
          log('Tokens loaded successfully', name: 'TokenService');
        }

        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        log('Error loading tokens: $e', name: 'TokenService');
      }
    }

    return false;
  }

  /// Clear all tokens
  static Future<bool> clearTokens() async {
    try {
      // Clear from memory
      Token.accessToken = null;
      Token.refreshToken = null;
      Token.expiresIn = null;

      // Clear from secure storage
      await SecureStorageHelper.instance.removeData(_tokenStorageKey);

      if (kDebugMode) {
        log('Tokens cleared successfully', name: 'TokenService');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        log('Error clearing tokens: $e', name: 'TokenService');
      }
      return false;
    }
  }

  /// Check if user has valid tokens
  static bool hasValidTokens() {
    return Token.accessToken != null &&
        Token.accessToken!.isNotEmpty &&
        Token.refreshToken != null &&
        Token.refreshToken!.isNotEmpty;
  }

  /// Refresh access token using refresh token
  static Future<bool> refreshAccessToken() async {
    if (Token.refreshToken == null || Token.refreshToken!.isEmpty) {
      if (kDebugMode) {
        log('No refresh token available', name: 'TokenService');
      }
      return false;
    }

    try {
      if (kDebugMode) {
        log('Starting token refresh with refresh token: ${Token.refreshToken?.substring(0, 10)}...',
            name: 'TokenService');
      }

      final dio = Dio();

      final formData = FormData.fromMap({
        'refresh_token': Token.refreshToken,
      });

      if (kDebugMode) {
        log('Calling refresh endpoint: ${ApiConstants.baseURL}refresh-token/',
            name: 'TokenService');
      }

      final response = await dio.post(
        '${ApiConstants.baseURL}refresh-token/',
        data: formData,
        options: Options(
          headers: await Api.headers(),
          validateStatus: (status) => true,
        ),
      );

      if (kDebugMode) {
        log('Refresh token response: ${response.statusCode}',
            name: 'TokenService');
        log('Refresh token response headers: ${response.headers}',
            name: 'TokenService');
        log('Refresh token data: ${jsonEncode(response.data)}',
            name: 'TokenService');
      }

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data;

        // Extract new tokens
        final newAccessToken = data['access_token'];
        final newRefreshToken = data['refresh_token'];
        final newExpiresIn = data['expires_in']?.toString();

        if (kDebugMode) {
          log('New access token received: ${newAccessToken?.substring(0, 10)}...',
              name: 'TokenService');
          log('New refresh token received: ${newRefreshToken?.substring(0, 10)}...',
              name: 'TokenService');
        }

        if (newAccessToken != null) {
          // Save new tokens
          final saveResult = await saveTokens(
            accessToken: newAccessToken,
            refreshToken: newRefreshToken ?? Token.refreshToken!,
            expiresIn: newExpiresIn,
          );

          if (kDebugMode) {
            log('Token save result: $saveResult', name: 'TokenService');
          }

          return saveResult;
        } else {
          if (kDebugMode) {
            log('No access token in refresh response', name: 'TokenService');
          }
        }
      } else {
        if (kDebugMode) {
          log('Refresh token failed with status: ${response.statusCode}',
              name: 'TokenService');
          log('Error response: ${response.data}', name: 'TokenService');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        log('Error refreshing token: $e', name: 'TokenService');
      }
    }

    return false;
  }

  /// Get current access token
  static String? getAccessToken() {
    return Token.accessToken;
  }

  /// Get current refresh token
  static String? getRefreshToken() {
    return Token.refreshToken;
  }

  /// Check if access token is expired (basic check)
  static bool isAccessTokenExpired() {
    // This is a basic implementation
    // You might want to implement proper JWT token expiry checking
    // based on the actual token format your API uses
    return Token.accessToken == null || Token.accessToken!.isEmpty;
  }

  /// Test the refresh token endpoint directly (for debugging)
  static Future<Map<String, dynamic>> testRefreshEndpoint() async {
    if (Token.refreshToken == null || Token.refreshToken!.isEmpty) {
      return {
        'success': false,
        'error': 'No refresh token available',
      };
    }

    try {
      final dio = Dio();

      if (kDebugMode) {
        log('Testing refresh endpoint directly...', name: 'TokenService');
      }

      final response = await dio.post(
        '${ApiConstants.baseURL}refresh-token/',
        data: FormData.fromMap({
          'refresh_token': Token.refreshToken,
        }),
        options: Options(
          headers: await Api.headers(),
          validateStatus: (status) => true,
        ),
      );

      return {
        'success': response.statusCode == 200,
        'statusCode': response.statusCode,
        'data': response.data,
        'headers': response.headers.map,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}
