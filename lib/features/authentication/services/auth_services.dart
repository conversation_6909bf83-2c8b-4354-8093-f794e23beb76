import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:albalad_parking_app/app/FCM.dart';
import 'package:albalad_parking_app/app/dio/dio_client.dart';
import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/helper/shared_preference_helper.dart';
import 'package:albalad_parking_app/utils/urls.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:encrypt/encrypt.dart' as aes;
import 'package:flutter/foundation.dart';

class AuthServices {
  final dio = DioClient().dio;

  Future<Response> signInWithMobileNumber(
      {required String mobileNumber}) async {
    // Fetch the encryption key asynchronously, which will be used to encrypt the data.
    final secretKey = await fetchEncryptionKey();

    // Convert the fetched key to a format suitable for AES encryption.
    final key = aes.Key.fromUtf8(secretKey.toString());

    // Generate an Initialization Vector (IV) with a fixed length of 16 bytes.
    // Note: AES in ECB mode does not use an IV; this is just a placeholder.
    final iv = aes.IV.fromLength(16);

    // Create an instance of the AES encrypter with the specified key and encryption mode (ECB in this case).
    final encrypter = aes.Encrypter(aes.AES(key, mode: aes.AESMode.ecb));

    // Encrypt the mobile number using the encrypter and the provided IV.
    // Note: ECB mode does not require an IV, so it is effectively ignored here.
    final encryptedMobileNumber = encrypter.encrypt(mobileNumber, iv: iv);
    if (kDebugMode) {
      print(jsonEncode({
        "mobile": encryptedMobileNumber.base64,
        "secret_key": secretKey,
        "encryption": "True"
      }));
    }
    final response = await dio.post(
      ApiConstants.signInURL,
      data: {
        "mobile": encryptedMobileNumber.base64,
        "secret_key": secretKey,
        "encryption": "True"
      },
      options: Options(
        headers: await Api.headers(),
        validateStatus: (status) => true,
      ),
    );

    if (kDebugMode) {
      print(response.statusCode);
      log(jsonEncode(response.data), name: "Sign In Response");
    }
    return response;
  }

  Future<String?> fetchEncryptionKey() async {
    final response = await dio.get(ApiConstants.secretKeyURL);
    // log("${response.realUri}" + jsonEncode(response.data), name: "Secret Key");
    if (response.statusCode == 200) {
      final json = response.data;
      if (json['result'] == 'success') {
        return json['secret_key'];
      }
    }
    return null;
  }

  Future<Map<String, dynamic>> getDeviceInfo() async {
    Map<String, dynamic> infoMap = {};
    // Create an instance of DeviceInfoPlugin to retrieve device-specific information.
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    // Create an instance of FirebaseMessaging to fetch the device's Firebase push notification token.
    // FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Check if the platform is Android.
    if (Platform.isAndroid) {
      // Retrieve Android-specific device information.
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

      // Add the device ID (unique identifier) to the request body.
      infoMap['primary'] = androidInfo.id;

      // Specify the platform type as Android.
      infoMap['platform'] = 'android';

      // Add the device model information (e.g., "Pixel 5").
      infoMap['model'] = androidInfo.model;

      // Add the manufacturer information (e.g., "Google").
      infoMap['manufacturer'] = androidInfo.manufacturer;
    } else {
      // Retrieve iOS-specific device information.
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;

      // Add the unique identifier for the iOS device to the request body.
      infoMap['primary'] = iosInfo.identifierForVendor;

      // Specify the platform type as iOS.
      infoMap['platform'] = 'ios';

      // Add the device model information (e.g., "iPhone14,3").
      infoMap['model'] = iosInfo.utsname.machine;

      // Specify the manufacturer as "Apple" for iOS devices.
      infoMap['manufacturer'] = 'apple';
    }

    // Retrieve the Firebase Cloud Messaging (FCM) token for the device and add it to the request body.
    infoMap['token'] = await FCM.token();
    return infoMap;
  }

  Future<Response> verifyOTP({
    required String verificationURL,
    required String otp,
  }) async {
    // Initialize an empty Map to hold the request body data.
    Map<String, dynamic> requestBody = {'otp': otp};

    requestBody.addAll(await getDeviceInfo());

    String? userLocale = SharedPreferenceHelper.instance.getData('locale');
    if (userLocale == 'en') {
      FCM.subscribeEnglishTopic();
    } else {
      FCM.subscribeArabicTopic();
    }

    final response = await dio.post(
      verificationURL,
      data: requestBody,
      options: Options(
        validateStatus: (status) => true,
        headers: await Api.headers(),
      ),
    );

    if (kDebugMode) {
      print(response.statusCode);
      log(jsonEncode(response.data), name: "Sign In otp");
    }

    return response;
  }

  Future<Response> resendOTP({required String resendOtpURL}) async {
    return await dio.post(
      resendOtpURL,
      options: Options(
        validateStatus: (status) => true,
        headers: await Api.headers(),
      ),
    );
  }

  Future<Response> signUpWithMobileNumber({
    required String fullName,
    required String mobileNumber,
    required String email,
  }) async {
    // Fetch the encryption key asynchronously, which will be used to encrypt the data.
    final secretKey = await fetchEncryptionKey();

    // Convert the fetched key to a format suitable for AES encryption.
    final key = aes.Key.fromUtf8(secretKey.toString());

    // Generate an Initialization Vector (IV) with a fixed length of 16 bytes.
    // Note: AES in ECB mode does not use an IV; this is just a placeholder.
    final iv = aes.IV.fromLength(16);

    // Create an instance of the AES encrypter with the specified key and encryption mode (ECB in this case).
    final encrypter = aes.Encrypter(aes.AES(key, mode: aes.AESMode.ecb));

    // Encrypt the mobile number using the encrypter and the provided IV.
    // Note: ECB mode does not require an IV, so it is effectively ignored here.
    final encryptedMobileNumber = encrypter.encrypt(mobileNumber, iv: iv);

    // String firstName = fullName.split(' ').first;
    // String? lastName = fullName.split(' ').lastOrNull;

    Map<String, dynamic> requestBody = {
      "first_name": fullName,
      // "last_name": lastName ?? '',
      "mobile": encryptedMobileNumber.base64,
      "secret_key": secretKey,
      "encryption": "True",
      "agree_terms": true
    };

    if (email.isNotEmpty) {
      final encryptedEmail = encrypter.encrypt(email, iv: iv);
      requestBody['email'] = encryptedEmail.base64;
    }

    final response = await dio.post(
      ApiConstants.signUpURL,
      data: requestBody,
      options: Options(
        headers: await Api.headers(),
        validateStatus: (status) => true,
      ),
    );

    if (kDebugMode) {
      log(jsonEncode(response.data), name: "Sign In Response");
    }
    return response;
  }

  Future<Response> getTermsAndConditions() async {
    final response = await dio.get(
      ApiConstants.termsAndConditionsURL,
      options: Options(
        validateStatus: (status) => true,
        headers: await Api.headers(),
      ),
    );
    debugPrint(response.statusCode.toString());
    if (kDebugMode) {
      log(jsonEncode(response.data), name: "Sign In Response");
    }
    return response;
  }

  Future<void> logout() async {
    try {
      await dio.post(
        ApiConstants.signOutURL,
        data: await getDeviceInfo(),
        options: Options(
          validateStatus: (status) => true,
          headers: await Api.getAuthorizationHeader(),
        ),
      );
    } finally {
      // Always clear tokens regardless of API response
      await TokenService.clearTokens();
    }
  }
}
