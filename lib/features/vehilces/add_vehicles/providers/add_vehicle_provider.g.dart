// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_vehicle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addVehicleNotifierHash() =>
    r'644dec3ca9d6a077b11b254f9453865c37acf83a';

/// See also [AddVehicleNotifier].
@ProviderFor(AddVehicleNotifier)
final addVehicleNotifierProvider =
    AutoDisposeNotifierProvider<AddVehicleNotifier, AddVehicelState>.internal(
  AddVehicleNotifier.new,
  name: r'addVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AddVehicleNotifier = AutoDisposeNotifier<AddVehicelState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
