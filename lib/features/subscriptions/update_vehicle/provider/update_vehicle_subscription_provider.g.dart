// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_vehicle_subscription_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$updateVehicleSubscriptionNotifeirHash() =>
    r'6fe0cc1eff569bfebaa57b2d9c6c35f662f160b6';

/// See also [UpdateVehicleSubscriptionNotifeir].
@ProviderFor(UpdateVehicleSubscriptionNotifeir)
final updateVehicleSubscriptionNotifeirProvider = AutoDisposeNotifierProvider<
    UpdateVehicleSubscriptionNotifeir, UpdateVehicleSubscriptionState>.internal(
  UpdateVehicleSubscriptionNotifeir.new,
  name: r'updateVehicleSubscriptionNotifeirProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$updateVehicleSubscriptionNotifeirHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateVehicleSubscriptionNotifeir
    = AutoDisposeNotifier<UpdateVehicleSubscriptionState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
