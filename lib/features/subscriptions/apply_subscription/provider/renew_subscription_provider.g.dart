// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'renew_subscription_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$renewSubscriptionNotifierHash() =>
    r'7bf06d9ceec1d1008282e09438c7e0dfa77acec1';

/// See also [RenewSubscriptionNotifier].
@ProviderFor(RenewSubscriptionNotifier)
final renewSubscriptionNotifierProvider = AutoDisposeNotifierProvider<
    RenewSubscriptionNotifier, RenewSubscriptionState>.internal(
  RenewSubscriptionNotifier.new,
  name: r'renewSubscriptionNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$renewSubscriptionNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RenewSubscriptionNotifier
    = AutoDisposeNotifier<RenewSubscriptionState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
