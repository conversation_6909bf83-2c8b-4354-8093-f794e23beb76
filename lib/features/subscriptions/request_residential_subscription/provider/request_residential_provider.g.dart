// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_residential_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$requestResidentialNotifierHash() =>
    r'3b84051730413264434a8729380dc1077146f804';

/// See also [RequestResidentialNotifier].
@ProviderFor(RequestResidentialNotifier)
final requestResidentialNotifierProvider = AutoDisposeNotifierProvider<
    RequestResidentialNotifier, RequestResidentialState>.internal(
  RequestResidentialNotifier.new,
  name: r'requestResidentialNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$requestResidentialNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RequestResidentialNotifier
    = AutoDisposeNotifier<RequestResidentialState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
