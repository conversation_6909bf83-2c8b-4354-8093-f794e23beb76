// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stay_connected_with_us_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$stayConnectedWithUsNotifierHash() =>
    r'c64fa7ded4bda41020da763949ea93793e8f729c';

/// See also [StayConnectedWithUsNotifier].
@ProviderFor(StayConnectedWithUsNotifier)
final stayConnectedWithUsNotifierProvider = AutoDisposeNotifierProvider<
    StayConnectedWithUsNotifier, StayConnectedState>.internal(
  StayConnectedWithUsNotifier.new,
  name: r'stayConnectedWithUsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$stayConnectedWithUsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$StayConnectedWithUsNotifier = AutoDisposeNotifier<StayConnectedState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
