// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'share_problem_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$formNotifierHash() => r'fa036dc2274ef403b0f59c1ed703814fe7f99de6';

/// See also [FormNotifier].
@ProviderFor(FormNotifier)
final formNotifierProvider =
    AutoDisposeNotifierProvider<FormNotifier, SeekHelpState>.internal(
  FormNotifier.new,
  name: r'formNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$formNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FormNotifier = AutoDisposeNotifier<SeekHelpState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
