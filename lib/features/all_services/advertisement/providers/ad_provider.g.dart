// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ad_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adNotifierHash() => r'be77d0aa8b796abdc3e7e8f23c5cb1cebcd4566a';

/// See also [AdNotifier].
@ProviderFor(AdNotifier)
final adNotifierProvider =
    AutoDisposeNotifierProvider<AdNotifier, AdState>.internal(
  AdNotifier.new,
  name: r'adNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$adNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AdNotifier = AutoDisposeNotifier<AdState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
