import 'dart:developer';
import 'package:albalad_parking_app/app/app.dart';
import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class AuthInterceptor extends Interceptor {
  bool _isNavigating = false;
  bool _isRefreshing = false;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add access token to requests if available
    if (Token.accessToken != null && Token.accessToken!.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer ${Token.accessToken}';
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // print('Response: ${response.statusCode}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Check if we have a refresh token and haven't started refreshing
      if (TokenService.hasValidTokens() && !_isRefreshing) {
        _isRefreshing = true;

        try {
          // Attempt to refresh the token
          final success = await TokenService.refreshAccessToken();

          if (success) {
            // Retry the original request with new token
            final retryResponse = await _retryRequest(err.requestOptions);
            _isRefreshing = false;
            handler.resolve(retryResponse);
            return;
          }
        } catch (e) {
          if (kDebugMode) {
            log('Token refresh failed: $e', name: 'AuthInterceptor');
          }
        }

        _isRefreshing = false;
      }

      // If refresh failed or no refresh token, redirect to login
      if (!_isNavigating) {
        _isNavigating = true;
        await _clearTokensAndRedirect();
      }
    }
    super.onError(err, handler);
  }

  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    final dio = Dio();

    // Update the authorization header with new token
    requestOptions.headers['Authorization'] = 'Bearer ${Token.accessToken}';

    return await dio.request(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        headers: requestOptions.headers,
        responseType: requestOptions.responseType,
        contentType: requestOptions.contentType,
        validateStatus: requestOptions.validateStatus,
        receiveTimeout: requestOptions.receiveTimeout,
        sendTimeout: requestOptions.sendTimeout,
      ),
    );
  }

  Future<void> _clearTokensAndRedirect() async {
    try {
      // Clear tokens using TokenService
      await TokenService.clearTokens();

      // Navigate to sign in screen
      navigatorKey.currentState
          ?.pushNamedAndRemoveUntil(
        SiginScreen.route,
        (route) => false,
      )
          .then((_) {
        _isNavigating = false;
      });
    } catch (e) {
      if (kDebugMode) {
        log('Error clearing tokens: $e', name: 'AuthInterceptor');
      }
      _isNavigating = false;
    }
  }
}
