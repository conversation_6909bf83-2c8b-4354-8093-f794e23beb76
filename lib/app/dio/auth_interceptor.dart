import 'dart:developer';
import 'package:albalad_parking_app/app/app.dart';
import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class AuthInterceptor extends Interceptor {
  bool _isNavigating = false;
  bool _isRefreshing = false;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add access token to requests if available
    if (Token.accessToken != null && Token.accessToken!.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer ${Token.accessToken}';
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (kDebugMode) {
      log('onResponse called with status: ${response.statusCode}',
          name: 'AuthInterceptor');
    }

    // Handle 401 responses for token refresh
    if (response.statusCode == 401) {
      if (kDebugMode) {
        log('401 detected in onResponse, handling...', name: 'AuthInterceptor');
      }
      await _handle401Response(response, handler);
      return;
    }

    // print('Response: ${response.statusCode}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (kDebugMode) {
      log('onError called - Type: ${err.type} - Message: ${err.message} - Status: ${err.response?.statusCode}',
          name: 'AuthInterceptor');
    }

    // Handle 401 errors as fallback (in case they come through onError instead of onResponse)
    if (err.response?.statusCode == 401) {
      if (kDebugMode) {
        log('401 detected in onError, handling...', name: 'AuthInterceptor');
      }
      await _handle401Error(err, handler);
      return;
    }

    super.onError(err, handler);
  }

  Future<void> _handle401Response(
      Response response, ResponseInterceptorHandler handler) async {
    // Check if we have a refresh token and haven't started refreshing
    if (TokenService.hasValidTokens() && !_isRefreshing) {
      _isRefreshing = true;

      try {
        if (kDebugMode) {
          log('Attempting to refresh token due to 401 response',
              name: 'AuthInterceptor');
        }

        // Attempt to refresh the token
        final success = await TokenService.refreshAccessToken();

        if (success) {
          if (kDebugMode) {
            log('Token refresh successful, retrying original request',
                name: 'AuthInterceptor');
          }

          // Retry the original request with new token
          final retryResponse = await _retryRequest(response.requestOptions);
          _isRefreshing = false;
          handler.resolve(retryResponse);
          return;
        } else {
          if (kDebugMode) {
            log('Token refresh failed', name: 'AuthInterceptor');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          log('Token refresh error: $e', name: 'AuthInterceptor');
        }
      }

      _isRefreshing = false;
    }

    // If refresh failed or no refresh token, redirect to login
    if (!_isNavigating) {
      _isNavigating = true;
      await _clearTokensAndRedirect();
      // Return the original 401 response
      handler.resolve(response);
    } else {
      handler.resolve(response);
    }
  }

  Future<void> _handle401Error(
      DioException err, ErrorInterceptorHandler handler) async {
    // Check if we have a refresh token and haven't started refreshing
    if (TokenService.hasValidTokens() && !_isRefreshing) {
      _isRefreshing = true;

      try {
        if (kDebugMode) {
          log('Attempting to refresh token due to 401 error',
              name: 'AuthInterceptor');
        }

        // Attempt to refresh the token
        final success = await TokenService.refreshAccessToken();

        if (success) {
          if (kDebugMode) {
            log('Token refresh successful, retrying original request',
                name: 'AuthInterceptor');
          }

          // Retry the original request with new token
          final retryResponse = await _retryRequest(err.requestOptions);
          _isRefreshing = false;
          handler.resolve(retryResponse);
          return;
        } else {
          if (kDebugMode) {
            log('Token refresh failed', name: 'AuthInterceptor');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          log('Token refresh error: $e', name: 'AuthInterceptor');
        }
      }

      _isRefreshing = false;
    }

    // If refresh failed or no refresh token, redirect to login
    if (!_isNavigating) {
      _isNavigating = true;
      await _clearTokensAndRedirect();
      // Reject the original error
      handler.reject(err);
    } else {
      handler.reject(err);
    }
  }

  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    // Create a new Dio instance without interceptors to avoid infinite loops
    final dio = Dio();

    // Copy base options from original request
    dio.options.baseUrl = requestOptions.baseUrl;
    dio.options.connectTimeout = requestOptions.connectTimeout;
    dio.options.receiveTimeout = requestOptions.receiveTimeout;
    dio.options.sendTimeout = requestOptions.sendTimeout;

    // Update the authorization header with new token
    final headers = Map<String, dynamic>.from(requestOptions.headers);
    headers['Authorization'] = 'Bearer ${Token.accessToken}';

    try {
      final response = await dio.request(
        requestOptions.path,
        data: requestOptions.data,
        queryParameters: requestOptions.queryParameters,
        options: Options(
          method: requestOptions.method,
          headers: headers,
          responseType: requestOptions.responseType,
          contentType: requestOptions.contentType,
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      if (kDebugMode) {
        log('Retry request successful: ${response.statusCode}',
            name: 'AuthInterceptor');
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        log('Retry request failed: $e', name: 'AuthInterceptor');
      }
      rethrow;
    }
  }

  Future<void> _clearTokensAndRedirect() async {
    if (kDebugMode) {
      log('_clearTokensAndRedirect called - isNavigating: $_isNavigating, isRefreshing: $_isRefreshing',
          name: 'AuthInterceptor');
    }

    // Don't navigate if we're currently refreshing tokens
    if (_isRefreshing) {
      if (kDebugMode) {
        log('Skipping navigation - token refresh in progress',
            name: 'AuthInterceptor');
      }
      return;
    }

    try {
      if (kDebugMode) {
        log('Clearing tokens and navigating to login', name: 'AuthInterceptor');
      }

      // Clear tokens using TokenService
      await TokenService.clearTokens();

      // Navigate to sign in screen
      navigatorKey.currentState
          ?.pushNamedAndRemoveUntil(
        SiginScreen.route,
        (route) => false,
      )
          .then((_) {
        _isNavigating = false;
        if (kDebugMode) {
          log('Navigation to login completed', name: 'AuthInterceptor');
        }
      });
    } catch (e) {
      if (kDebugMode) {
        log('Error clearing tokens: $e', name: 'AuthInterceptor');
      }
      _isNavigating = false;
    }
  }
}
