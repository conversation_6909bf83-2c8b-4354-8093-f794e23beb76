import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'dart:developer';
import 'auth_interceptor.dart';

class DioClient {
  final Dio _dio = Dio();

  DioClient() {
    _dio.interceptors.add(AuthInterceptor());

    // Add logging interceptor for debugging
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: true,
        logPrint: (obj) => log(obj.toString(), name: 'DioClient'),
      ));
    }

    // Configure validateStatus to treat 401 as successful response
    // so it goes to onResponse instead of onError
    _dio.options.validateStatus = (status) {
      if (kDebugMode) {
        log('ValidateStatus called with: $status', name: 'DioClient');
      }
      // Treat all status codes as "successful" so they go to onResponse
      // The AuthInterceptor will handle 401s appropriately
      return status != null && status >= 200 && status < 500;
    };
  }

  Dio get dio => _dio;
}

// RRYeyNsffdcc640gzOTpXsB6YX4KGJ
