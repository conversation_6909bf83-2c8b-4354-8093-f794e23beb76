import 'package:dio/dio.dart';
import 'auth_interceptor.dart';

class DioClient {
  final Dio _dio = Dio();

  DioClient() {
    _dio.interceptors.add(AuthInterceptor());
    // Remove the custom validateStatus that treats 401 as error
    // Let the AuthInterceptor handle 401 responses properly
    _dio.options.validateStatus = (status) {
      return status != null && status < 500;
    };
  }

  Dio get dio => _dio;
}

// a4JhTy3e0xuEupjg4eUeaKGu4gzSPn
