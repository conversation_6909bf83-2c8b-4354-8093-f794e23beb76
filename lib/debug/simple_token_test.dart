import 'dart:developer';
import 'package:albalad_parking_app/app/dio/dio_client.dart';
import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:albalad_parking_app/utils/urls.dart';
import 'package:flutter/foundation.dart';

/// Simple test functions to debug token refresh issues
class SimpleTokenTest {
  
  /// Test the complete flow step by step
  static Future<void> debugTokenRefreshFlow() async {
    if (!kDebugMode) return;
    
    log('=== STARTING TOKEN REFRESH DEBUG ===', name: 'SimpleTokenTest');
    
    // Step 1: Check current token status
    log('Step 1: Current token status', name: 'SimpleTokenTest');
    log('Access Token exists: ${Token.accessToken != null}', name: 'SimpleTokenTest');
    log('Refresh Token exists: ${Token.refreshToken != null}', name: 'SimpleTokenTest');
    log('Has Valid Tokens: ${TokenService.hasValidTokens()}', name: 'SimpleTokenTest');
    
    if (!TokenService.hasValidTokens()) {
      log('ERROR: No valid tokens found. Please login first.', name: 'SimpleTokenTest');
      return;
    }
    
    // Step 2: Test refresh endpoint directly
    log('Step 2: Testing refresh endpoint directly', name: 'SimpleTokenTest');
    final refreshResult = await TokenService.testRefreshEndpoint();
    log('Refresh endpoint result: $refreshResult', name: 'SimpleTokenTest');
    
    if (refreshResult['success'] != true) {
      log('ERROR: Refresh endpoint failed. Check API configuration.', name: 'SimpleTokenTest');
      return;
    }
    
    // Step 3: Simulate invalid token and test interceptor
    log('Step 3: Simulating invalid token', name: 'SimpleTokenTest');
    final originalToken = Token.accessToken;
    Token.accessToken = 'invalid_token_for_testing';
    
    try {
      // Step 4: Make authenticated request
      log('Step 4: Making authenticated request with invalid token', name: 'SimpleTokenTest');
      final dio = DioClient().dio;
      
      // Use a simple endpoint that requires authentication
      final response = await dio.get('${ApiConstants.baseURL}profile/');
      
      log('Request completed successfully: ${response.statusCode}', name: 'SimpleTokenTest');
      log('Token refresh worked! New access token: ${Token.accessToken?.substring(0, 10)}...', name: 'SimpleTokenTest');
      
    } catch (e) {
      log('Request failed: $e', name: 'SimpleTokenTest');
      log('Token refresh did not work. Check interceptor logic.', name: 'SimpleTokenTest');
      
      // Restore original token
      Token.accessToken = originalToken;
    }
    
    log('=== TOKEN REFRESH DEBUG COMPLETED ===', name: 'SimpleTokenTest');
  }
  
  /// Test just the refresh token API call
  static Future<void> testRefreshTokenAPI() async {
    if (!kDebugMode) return;
    
    log('=== TESTING REFRESH TOKEN API ===', name: 'SimpleTokenTest');
    
    if (Token.refreshToken == null || Token.refreshToken!.isEmpty) {
      log('ERROR: No refresh token available', name: 'SimpleTokenTest');
      return;
    }
    
    final result = await TokenService.testRefreshEndpoint();
    log('API Test Result: $result', name: 'SimpleTokenTest');
    
    if (result['success'] == true) {
      log('✅ Refresh token API is working correctly', name: 'SimpleTokenTest');
    } else {
      log('❌ Refresh token API failed', name: 'SimpleTokenTest');
      log('Status Code: ${result['statusCode']}', name: 'SimpleTokenTest');
      log('Response Data: ${result['data']}', name: 'SimpleTokenTest');
    }
    
    log('=== REFRESH TOKEN API TEST COMPLETED ===', name: 'SimpleTokenTest');
  }
  
  /// Check if interceptor is being called
  static Future<void> testInterceptorFlow() async {
    if (!kDebugMode) return;
    
    log('=== TESTING INTERCEPTOR FLOW ===', name: 'SimpleTokenTest');
    
    // Save original token
    final originalToken = Token.accessToken;
    
    try {
      // Set invalid token
      Token.accessToken = 'definitely_invalid_token';
      log('Set invalid access token', name: 'SimpleTokenTest');
      
      // Make request
      final dio = DioClient().dio;
      log('Making request with invalid token...', name: 'SimpleTokenTest');
      
      final response = await dio.get('${ApiConstants.baseURL}profile/');
      log('Response received: ${response.statusCode}', name: 'SimpleTokenTest');
      
      if (response.statusCode == 200) {
        log('✅ Interceptor worked! Token was refreshed.', name: 'SimpleTokenTest');
      } else {
        log('❌ Unexpected response: ${response.statusCode}', name: 'SimpleTokenTest');
      }
      
    } catch (e) {
      log('❌ Request failed: $e', name: 'SimpleTokenTest');
      log('This suggests the interceptor did not handle the 401 properly', name: 'SimpleTokenTest');
      
      // Restore original token
      Token.accessToken = originalToken;
    }
    
    log('=== INTERCEPTOR FLOW TEST COMPLETED ===', name: 'SimpleTokenTest');
  }
}
