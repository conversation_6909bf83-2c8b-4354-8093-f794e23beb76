import 'dart:developer';
import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:flutter/foundation.dart';

/// Debug helper class for testing token refresh functionality
class TokenDebugHelper {
  /// Print current token status
  static void printTokenStatus() {
    if (kDebugMode) {
      log('=== TOKEN STATUS ===', name: 'TokenDebugHelper');
      log('Access Token: ${Token.accessToken?.substring(0, 20)}...',
          name: 'TokenDebugHelper');
      log('Refresh Token: ${Token.refreshToken?.substring(0, 20)}...',
          name: 'TokenDebugHelper');
      log('Expires In: ${Token.expiresIn}', name: 'TokenDebugHelper');
      log('Has Valid Tokens: ${TokenService.hasValidTokens()}',
          name: 'TokenDebugHelper');
      log('==================', name: 'TokenDebugHelper');
    }
  }

  /// Manually trigger token refresh for testing
  static Future<bool> testTokenRefresh() async {
    if (kDebugMode) {
      log('=== TESTING TOKEN REFRESH ===', name: 'TokenDebugHelper');
      printTokenStatus();

      final result = await TokenService.refreshAccessToken();

      log('Refresh Result: $result', name: 'TokenDebugHelper');
      printTokenStatus();
      log('============================', name: 'TokenDebugHelper');

      return result;
    }
    return false;
  }

  /// Simulate an expired access token by clearing it
  static void simulateExpiredAccessToken() {
    if (kDebugMode) {
      log('Simulating expired access token by clearing it',
          name: 'TokenDebugHelper');
      Token.accessToken = null;
      printTokenStatus();
    }
  }

  /// Simulate an invalid access token
  static void simulateInvalidAccessToken() {
    if (kDebugMode) {
      log('Simulating invalid access token', name: 'TokenDebugHelper');
      Token.accessToken = 'invalid_token_for_testing';
      printTokenStatus();
    }
  }

  /// Clear all tokens for testing
  static Future<void> clearAllTokens() async {
    if (kDebugMode) {
      log('Clearing all tokens for testing', name: 'TokenDebugHelper');
      await TokenService.clearTokens();
      printTokenStatus();
    }
  }

  /// Load tokens from storage
  static Future<void> loadTokens() async {
    if (kDebugMode) {
      log('Loading tokens from storage', name: 'TokenDebugHelper');
      await TokenService.loadTokens();
      printTokenStatus();
    }
  }

  /// Test the refresh endpoint directly
  static Future<Map<String, dynamic>> testRefreshEndpoint() async {
    if (kDebugMode) {
      log('=== TESTING REFRESH ENDPOINT ===', name: 'TokenDebugHelper');
      final result = await TokenService.testRefreshEndpoint();
      log('Endpoint test result: $result', name: 'TokenDebugHelper');
      log('===============================', name: 'TokenDebugHelper');
      return result;
    }
    return {'success': false, 'error': 'Debug mode not enabled'};
  }
}
