import 'package:albalad_parking_app/app/dio/dio_client.dart';
import 'package:albalad_parking_app/debug/token_debug_helper.dart';
import 'package:albalad_parking_app/debug/simple_token_test.dart';
import 'package:albalad_parking_app/utils/urls.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

/// Debug screen for testing token refresh functionality
/// Add this to your app temporarily for testing
class TokenTestScreen extends StatefulWidget {
  static const String route = '/token-test';

  const TokenTestScreen({super.key});

  @override
  State<TokenTestScreen> createState() => _TokenTestScreenState();
}

class _TokenTestScreenState extends State<TokenTestScreen> {
  String _lastResult = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    TokenDebugHelper.printTokenStatus();
  }

  Future<void> _makeAuthenticatedRequest() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Making authenticated request...';
    });

    try {
      final dio = DioClient().dio;

      // Make a request that requires authentication
      // Replace with any endpoint that requires auth in your app
      final response = await dio.get('${ApiConstants.baseURL}profile/');

      setState(() {
        _lastResult = 'Success: ${response.statusCode}\n${response.data}';
      });
    } catch (e) {
      setState(() {
        _lastResult = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSpecific401() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing specific 401 scenario...';
    });

    try {
      // First, invalidate the access token
      TokenDebugHelper.simulateInvalidAccessToken();

      // Wait a moment
      await Future.delayed(const Duration(milliseconds: 500));

      // Now make a request that should trigger refresh
      final dio = DioClient().dio;
      final response = await dio.get('${ApiConstants.baseURL}profile/');

      setState(() {
        _lastResult =
            'Test completed successfully: ${response.statusCode}\nToken refresh worked!';
      });
    } catch (e) {
      setState(() {
        _lastResult = 'Test failed: $e\nCheck console logs for details';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testTokenRefresh() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing token refresh...';
    });

    try {
      final result = await TokenDebugHelper.testTokenRefresh();
      setState(() {
        _lastResult = 'Token refresh result: $result';
      });
    } catch (e) {
      setState(() {
        _lastResult = 'Token refresh error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Token Test Screen'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Token Refresh Testing',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      TokenDebugHelper.printTokenStatus();
                      setState(() {
                        _lastResult = 'Check console for token status';
                      });
                    },
              child: const Text('Print Token Status'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _testTokenRefresh,
              child: const Text('Test Token Refresh'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () async {
                      setState(() {
                        _isLoading = true;
                        _lastResult = 'Testing refresh endpoint...';
                      });

                      try {
                        final result =
                            await TokenDebugHelper.testRefreshEndpoint();
                        setState(() {
                          _lastResult =
                              'Endpoint test result:\n${result.toString()}';
                        });
                      } catch (e) {
                        setState(() {
                          _lastResult = 'Endpoint test error: $e';
                        });
                      } finally {
                        setState(() {
                          _isLoading = false;
                        });
                      }
                    },
              child: const Text('Test Refresh Endpoint'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      TokenDebugHelper.simulateInvalidAccessToken();
                      setState(() {
                        _lastResult = 'Simulated invalid access token';
                      });
                    },
              child: const Text('Simulate Invalid Token'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _makeAuthenticatedRequest,
              child: const Text('Make Authenticated Request'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _testSpecific401,
              child: const Text('Test 401 → Refresh Flow'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () async {
                      await TokenDebugHelper.clearAllTokens();
                      setState(() {
                        _lastResult = 'All tokens cleared';
                      });
                    },
              child: const Text('Clear All Tokens'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () async {
                      setState(() {
                        _isLoading = true;
                        _lastResult = 'Running complete debug flow...';
                      });

                      try {
                        await SimpleTokenTest.debugTokenRefreshFlow();
                        setState(() {
                          _lastResult =
                              'Debug flow completed. Check console for detailed logs.';
                        });
                      } catch (e) {
                        setState(() {
                          _lastResult = 'Debug flow error: $e';
                        });
                      } finally {
                        setState(() {
                          _isLoading = false;
                        });
                      }
                    },
              child: const Text('🔍 Complete Debug Flow'),
            ),
            const SizedBox(height: 20),
            if (_isLoading) const Center(child: CircularProgressIndicator()),
            const Text(
              'Last Result:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _lastResult.isEmpty ? 'No results yet' : _lastResult,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Instructions:\n'
              '1. Login to your app first\n'
              '2. Use "Print Token Status" to see current tokens\n'
              '3. Use "Simulate Invalid Token" to force a 401\n'
              '4. Use "Make Authenticated Request" to test refresh\n'
              '5. Check console logs for detailed debugging',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
