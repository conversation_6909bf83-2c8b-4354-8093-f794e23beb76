import 'package:albalad_parking_app/features/authentication/services/token_service.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TokenService', () {
    setUp(() {
      // Reset tokens before each test
      Token.accessToken = null;
      Token.refreshToken = null;
      Token.expiresIn = null;
    });

    group('saveTokens', () {
      test('should save tokens to memory', () async {
        // Arrange
        const accessToken = 'test_access_token';
        const refreshToken = 'test_refresh_token';
        const expiresIn = '3600';

        // Act
        await TokenService.saveTokens(
          accessToken: accessToken,
          refreshToken: refreshToken,
          expiresIn: expiresIn,
        );

        // Assert
        expect(Token.accessToken, accessToken);
        expect(Token.refreshToken, refreshToken);
        expect(Token.expiresIn, expiresIn);
      });
    });

    group('clearTokens', () {
      test('should clear tokens from memory', () async {
        // Arrange
        Token.accessToken = 'test_access_token';
        Token.refreshToken = 'test_refresh_token';
        Token.expiresIn = '3600';

        // Act
        await TokenService.clearTokens();

        // Assert
        expect(Token.accessToken, null);
        expect(Token.refreshToken, null);
        expect(Token.expiresIn, null);
      });
    });

    group('hasValidTokens', () {
      test('should return true when both tokens exist', () {
        // Arrange
        Token.accessToken = 'test_access_token';
        Token.refreshToken = 'test_refresh_token';

        // Act & Assert
        expect(TokenService.hasValidTokens(), true);
      });

      test('should return false when access token is missing', () {
        // Arrange
        Token.accessToken = null;
        Token.refreshToken = 'test_refresh_token';

        // Act & Assert
        expect(TokenService.hasValidTokens(), false);
      });

      test('should return false when refresh token is missing', () {
        // Arrange
        Token.accessToken = 'test_access_token';
        Token.refreshToken = null;

        // Act & Assert
        expect(TokenService.hasValidTokens(), false);
      });

      test('should return false when tokens are empty strings', () {
        // Arrange
        Token.accessToken = '';
        Token.refreshToken = '';

        // Act & Assert
        expect(TokenService.hasValidTokens(), false);
      });
    });

    group('getAccessToken', () {
      test('should return current access token', () {
        // Arrange
        const accessToken = 'test_access_token';
        Token.accessToken = accessToken;

        // Act & Assert
        expect(TokenService.getAccessToken(), accessToken);
      });
    });

    group('getRefreshToken', () {
      test('should return current refresh token', () {
        // Arrange
        const refreshToken = 'test_refresh_token';
        Token.refreshToken = refreshToken;

        // Act & Assert
        expect(TokenService.getRefreshToken(), refreshToken);
      });
    });

    group('isAccessTokenExpired', () {
      test('should return true when access token is null', () {
        // Arrange
        Token.accessToken = null;

        // Act & Assert
        expect(TokenService.isAccessTokenExpired(), true);
      });

      test('should return true when access token is empty', () {
        // Arrange
        Token.accessToken = '';

        // Act & Assert
        expect(TokenService.isAccessTokenExpired(), true);
      });

      test('should return false when access token exists', () {
        // Arrange
        Token.accessToken = 'valid_token';

        // Act & Assert
        expect(TokenService.isAccessTokenExpired(), false);
      });
    });
  });
}
