name: albalad_parking_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_localizations:
    sdk: flutter
  intl: any
  flutter_riverpod: ^2.6.1
  dio: ^5.8.0+1
  shared_preferences: ^2.5.2
  flutter_screenutil: ^5.9.3
  percent_indicator: ^4.2.4
  flutter_svg: ^2.0.17
  gap: ^3.0.1
  fluttertoast: ^8.2.12
  timeago: ^3.7.0
  cached_network_image: ^3.4.1
  riverpod_annotation: ^2.6.1
  encrypt: ^5.0.3
  flutter_hooks: ^0.20.5
  phone_numbers_parser: ^9.0.3
  google_maps_flutter: ^2.10.0
  hooks_riverpod: ^2.6.1
  pinput: ^5.0.0
  circular_gradient_spinner: ^0.0.3
  geolocator: ^13.0.4
  country_code_picker: ^3.1.0
  country_picker: ^2.0.27
  device_info_plus: ^11.2.0
  firebase_core: ^3.12.1
  firebase_messaging: ^15.2.4
  flutter_easyloading: ^3.0.5
  image_cropper: ^8.1.0
  image_picker: ^1.1.2
  flutter_phoenix: ^1.1.1
  dotted_border: ^2.1.0
  # ocr_scan_text: ^1.3.1
  permission_handler: ^11.3.1
  flutter_slidable: ^3.1.2
  infinite_scroll_pagination: ^4.1.0
  equatable: ^2.0.7
  translator: ^1.0.3+1
  flutter_scalable_ocr:
    path: flutter_scalable_ocr
  url_launcher: ^6.3.1
  flutter_html: ^3.0.0-beta.2
  dotted_line: ^3.2.3
  easy_date_formatter: ^0.0.1
  qr_code_scanner_plus: ^2.0.9+1
  flutter_timezone: ^4.0.0
  webview_flutter: ^4.10.0
  hyperpay_plugin: ^3.1.4
  provider: ^6.1.2
  get_time_ago: ^2.2.0
  highlight_text: ^1.8.0
  skeletonizer: ^2.0.1
  app_links: ^6.4.0
  photo_view: ^0.15.0
  flutter_local_notifications: ^18.0.1
  open_file: ^3.5.10
  path_provider: ^2.1.5
  auto_size_text: ^3.0.0
  package_info_plus: ^8.3.0
  pub_semver: ^2.2.0
  store_redirect: ^2.0.4
  live_activities: ^2.3.1
  uuid: ^4.5.1
  flutter_secure_storage: ^9.0.0
  # flutter_rating_stars: ^1.1.0
  flutter_rating_bar: ^4.0.1
  firebase_crashlytics: ^4.3.5
  firebase_analytics: ^11.4.5


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  riverpod_generator: ^2.6.3
  build_runner: ^2.4.14
  custom_lint: ^0.7.0
  riverpod_lint: ^2.6.3
  mockito: ^5.4.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  disable-swift-package-manager: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/
    - assets/svgs/
    - assets/google_map/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SF-Pro-Display
      fonts:
        - asset: fonts/SF-Pro-Display/FontsFree-Net-SFProDisplay-Regular.ttf
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-Black.otf
          weight: 900
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-BlackItalic.otf
          weight: 900
          style: italic
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-BoldItalic.otf
          weight: 700
          style: italic
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-Heavy.otf
          weight: 800
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-HeavyItalic.otf
          weight: 800
          style: italic
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-Light.otf
          weight: 300
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-LightItalic.otf
          weight: 300
          style: italic
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-Medium.otf
          weight: 500
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-MediumItalic.otf
          weight: 500
          style: italic
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-RegularItalic.otf
          weight: 400
          style: italic
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-Semibold.otf
          weight: 600
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-Thin.otf
          weight: 100
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-ThinItalic.otf
          weight: 100
          style: italic
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-Ultralight.otf
          weight: 200
        - asset: fonts/SF-Pro-Display/SF-Pro-Display-UltralightItalic.otf
          weight: 200
          style: italic
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
