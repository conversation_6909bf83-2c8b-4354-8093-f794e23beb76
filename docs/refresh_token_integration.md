# Refresh Token Integration

This document describes the refresh token system implementation for the Al-Balad parking app.

## Overview

The refresh token system automatically handles token refresh when API calls receive a 401 (Unauthorized) response. This ensures users stay logged in without manual intervention.

## Components

### 1. TokenService (`lib/features/authentication/services/token_service.dart`)

A centralized service for managing authentication tokens:

- **saveTokens()**: Saves access and refresh tokens to both memory and secure storage
- **loadTokens()**: Loads tokens from secure storage on app startup
- **clearTokens()**: Clears all tokens from memory and storage
- **refreshAccessToken()**: Calls the refresh token API endpoint
- **hasValidTokens()**: Checks if valid tokens exist
- **isAccessTokenExpired()**: Basic token expiry check

### 2. AuthInterceptor (`lib/app/dio/auth_interceptor.dart`)

Enhanced Dio interceptor that:

- Automatically adds access tokens to outgoing requests
- Intercepts 401 responses and attempts token refresh
- Retries failed requests with new tokens
- Redirects to login if refresh fails

### 3. Integration Points

- **OTP Verification**: Saves tokens after successful login
- **Splash Screen**: Loads tokens on app startup
- **Logout**: Clears tokens using TokenService

## API Endpoint

The refresh token endpoint is configured as:

```
URL: {baseURL}refresh-token/
Method: POST
Content-Type: multipart/form-data
Body: refresh_token={current_refresh_token}
```

## How It Works

1. **App Startup**: Tokens are loaded from secure storage
2. **API Requests**: Access token is automatically added to headers
3. **Token Expiry**: When a 401 response is received:
   - AuthInterceptor attempts to refresh the token
   - If successful, the original request is retried with the new token
   - If refresh fails, user is redirected to login screen
4. **Token Storage**: New tokens are saved to secure storage

## Testing the Implementation

### Manual Testing

1. **Login Flow**:
   - Complete the OTP verification process
   - Verify tokens are saved in secure storage
   - Check that subsequent API calls include the access token

2. **Token Refresh**:
   - Wait for access token to expire (or manually invalidate it)
   - Make an API call that requires authentication
   - Verify the interceptor automatically refreshes the token
   - Confirm the original request succeeds with the new token

3. **Logout Flow**:
   - Call the logout function
   - Verify all tokens are cleared from storage
   - Confirm user is redirected to login screen

### Automated Testing

Run the unit tests for TokenService:

```bash
flutter test test/unit/features/authentication/services/token_service_test.dart
```

## Security Considerations

1. **Secure Storage**: Tokens are stored using Flutter Secure Storage
2. **Token Rotation**: Both access and refresh tokens can be updated
3. **Automatic Cleanup**: Tokens are cleared on logout and refresh failure
4. **Single Refresh**: Prevents multiple simultaneous refresh attempts

## Configuration

The refresh token endpoint URL is configured in `lib/utils/urls.dart`:

```dart
static String get baseURL => '$apiBaseURL/api/customer/v1/';
// Refresh endpoint: {baseURL}refresh-token/
```

## Error Handling

- **Network Errors**: Logged and handled gracefully
- **Invalid Refresh Token**: User redirected to login
- **API Errors**: Detailed logging for debugging
- **Storage Errors**: Fallback to memory-only operation

## Future Enhancements

1. **JWT Token Parsing**: Implement proper token expiry checking
2. **Background Refresh**: Proactively refresh tokens before expiry
3. **Multiple Device Support**: Handle token conflicts across devices
4. **Retry Logic**: Implement exponential backoff for failed refresh attempts

## Troubleshooting

### Common Issues

1. **Tokens Not Persisting**: Check secure storage permissions
2. **Infinite Refresh Loop**: Verify refresh token endpoint response format
3. **Login Redirect Instead of Refresh**: Check DioClient validateStatus configuration
4. **API Errors**: Check network connectivity and endpoint configuration
5. **401 Errors Not Triggering Refresh**: Ensure AuthInterceptor is properly configured

### Debug Tools

Use the provided debug tools for testing:

1. **TokenDebugHelper** (`lib/debug/token_debug_helper.dart`):
   ```dart
   // Print current token status
   TokenDebugHelper.printTokenStatus();

   // Test token refresh manually
   await TokenDebugHelper.testTokenRefresh();

   // Simulate expired token
   TokenDebugHelper.simulateInvalidAccessToken();
   ```

2. **TokenTestScreen** (`lib/debug/token_test_screen.dart`):
   - Add to your app routes for visual testing
   - Provides buttons to test various scenarios
   - Shows real-time results and logs

### Debug Logging

The system provides extensive debug logging. Enable debug mode to see:

```dart
if (kDebugMode) {
  log('Token refresh response: ${response.statusCode}', name: 'TokenService');
  log('Attempting to refresh token due to 401 response', name: 'AuthInterceptor');
}
```

### Step-by-Step Debugging

1. **Check Token Status**:
   ```dart
   TokenDebugHelper.printTokenStatus();
   ```

2. **Verify Refresh Endpoint**:
   - Ensure `{baseURL}refresh-token/` is correct
   - Check API expects `multipart/form-data`
   - Verify `refresh_token` field name

3. **Test Manual Refresh**:
   ```dart
   final success = await TokenService.refreshAccessToken();
   print('Manual refresh result: $success');
   ```

4. **Check DioClient Configuration**:
   - Verify `validateStatus` allows 401 responses
   - Ensure AuthInterceptor is added to interceptors

5. **Monitor Network Requests**:
   - Use network debugging tools
   - Check if 401 responses reach the interceptor
   - Verify refresh requests are being made

### Expected Log Flow

When working correctly, you should see logs like:
```
AuthInterceptor: Attempting to refresh token due to 401 response
TokenService: Starting token refresh with refresh token: abcd1234...
TokenService: Calling refresh endpoint: https://api.example.com/refresh-token/
TokenService: Refresh token response: 200
TokenService: New access token received: xyz9876...
AuthInterceptor: Token refresh successful, retrying original request
AuthInterceptor: Retry request successful: 200
```

## Migration Notes

If upgrading from a previous authentication system:

1. Existing tokens may need to be migrated to the new storage format
2. Update all logout flows to use `TokenService.clearTokens()`
3. Ensure all API services use the enhanced DioClient with AuthInterceptor
